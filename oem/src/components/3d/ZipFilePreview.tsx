import BodyText, { BODY_TEXT_SIZES } from "@shared/ui/BodyText";
import { SearchInput } from "@shared/ui/Inputs";
import JSZip from "jszip";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useIntl as useInternationalization } from "react-intl";
import {
  AutoSizer,
  List,
  CellMeasurerCache,
  CellMeasurer,
} from "react-virtualized";

const ZipFilePreview = ({
  inputFile,
  onMainFileSelect,
}: {
  inputFile: File;
  onMainFileSelect?: (selectedFile: string) => void;
}) => {
  const [filePaths, setFilePaths] = useState<string[]>([]);
  const [selectedMainFile, setSelectedMainFile] = useState<string>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { messages } = useInternationalization();

  const cache = new CellMeasurerCache({
    fixedWidth: true,
    minHeight: 50,
  });

  const isValidFile = (path: string): boolean => {
    if (!path) return false;

    const fileName = path.split("/").pop();
    if (!fileName) return false;

    const parts = fileName.split(".");

    return (
      parts.length >= 2 &&
      parts[0].length > 0 &&
      parts[parts.length - 1].length > 0
    );
  };

  const processZipInChunks = useCallback(
    async (zip: JSZip): Promise<string[]> => {
      const fileEntries = Object.entries(zip.files);
      const validPaths: string[] = [];
      const chunkSize = 50; // Process files in chunks to avoid blocking UI

      for (let i = 0; i < fileEntries.length; i += chunkSize) {
        const chunk = fileEntries.slice(i, i + chunkSize);

        const chunkPaths = chunk
          .filter(([path, file]) => !file.dir && isValidFile(path))
          .map(([path]) => path);

        validPaths.push(...chunkPaths);

        if (i + chunkSize < fileEntries.length) {
          await new Promise((resolve) => setTimeout(resolve, 0));
        }
      }

      return validPaths;
    },
    [],
  );

  const handleZipChange = useCallback(async () => {
    const file = inputFile;

    if (!file || file.type !== "application/zip") return;

    setIsLoading(true);
    setError(null);
    setFilePaths([]);

    try {
      const zip = new JSZip();
      const arrayBuffer = await file.arrayBuffer();

      const contents = await zip.loadAsync(arrayBuffer, {
        checkCRC32: false,
      });

      const validPaths = await processZipInChunks(contents);

      setFilePaths(validPaths);
    } catch (err) {
      setError(messages.machines?.["zipFilePreviewErrorMessage"]);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inputFile, processZipInChunks]);

  const handleMainFileChange = (selectedFile: string) => {
    setSelectedMainFile(selectedFile);
    onMainFileSelect?.(selectedFile);
  };

  useEffect(() => {
    if (inputFile) {
      handleZipChange();
    }
  }, [inputFile, handleZipChange]);

  const filteredFilePaths = useMemo(() => {
    return filePaths.filter((path) =>
      path.toLowerCase().includes(searchQuery.trim().toLowerCase()),
    );
  }, [filePaths, searchQuery]);

  const RadioOption = ({
    path,
    isSelected,
    onChange,
  }: {
    path: string;
    isSelected: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  }) => {
    const radioInputClasses: string =
      "m-0 appearance-none w-lg h-lg cursor-pointer rounded-full bg-primary border-solid flex-shrink-0";

    const fileName = path.split("/").pop();
    const pathWithoutZipFolder = path.split("/").slice(1).join("/");
    const pathSegments = pathWithoutZipFolder.split("/");

    return (
      <div key={path} className="flex items-center gap-md">
        <input
          type="radio"
          id={`mainAssemblyFileSelection-${path}`}
          name="mainAssemblyFileSelection"
          value={path}
          onChange={onChange}
          checked={isSelected}
          className={`${radioInputClasses} ${
            isSelected ? "border-accent border-4" : "border-secondary border"
          }`}
        />
        <div className="flex flex-col flex-1 min-w-0">
          <div className="flex flex-col items-start w-full">
            <label
              htmlFor={`mainAssemblyFileSelection-${path}`}
              className="font-manrope font-bold text-md cursor-pointer text-primary truncate w-full"
              title={fileName}
            >
              {fileName}
            </label>
          </div>
          {pathSegments.length > 1 && (
            <div className="flex flex-col items-start w-full">
              <label
                htmlFor={`mainAssemblyFileSelection-${path}`}
                className="font-manrope font-bold text-xs cursor-pointer text-primary truncate w-full"
                title={pathWithoutZipFolder}
              >
                {pathSegments.map((segment, index, array) => (
                  <span key={index}>
                    <span className="text-secondary font-medium">{` ${segment}`}</span>
                    {index < array.length - 1 && (
                      <span className="text-tertiary mx-1">/</span>
                    )}
                  </span>
                ))}
              </label>
            </div>
          )}
        </div>
      </div>
    );
  };

  const rowRenderer = ({
    index,
    key,
    style,
    parent,
  }: {
    index: number;
    key: string;
    style: React.CSSProperties;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    parent: any;
  }) => {
    const path = filteredFilePaths[index];
    return (
      <CellMeasurer
        cache={cache}
        columnIndex={0}
        key={key}
        rowIndex={index}
        parent={parent}
      >
        {({ measure, registerChild }) => (
          <div ref={registerChild} style={style}>
            <div
              className="border-solid border-[1px] rounded-lg border-primary p-lg mb-sm"
              onClick={() => handleMainFileChange(path)}
              onLoad={measure}
            >
              <RadioOption
                path={path}
                isSelected={selectedMainFile === path}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  const selectedFile = e.target.value;
                  handleMainFileChange(selectedFile);
                }}
              />
            </div>
          </div>
        )}
      </CellMeasurer>
    );
  };

  if (error) {
    return (
      <div className="flex flex-col align-center justify-center h-9xl">
        <BodyText
          size={BODY_TEXT_SIZES.X_SMALL}
          color="text-secondary text-center"
        >
          {messages.machines?.["zipFilePreviewErrorMessage"]}
        </BodyText>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <div className="lds-ring">
          <div
            style={{
              borderColor: "#0517F8 transparent transparent transparent",
            }}
          ></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-sm">
      <BodyText color="text-secondary" size={BODY_TEXT_SIZES.X_SMALL}>
        {messages.machines?.["selectParentFile"]} *
      </BodyText>

      <div className="flex flex-col">
        {filePaths.length === 0 ? (
          <BodyText size={BODY_TEXT_SIZES.SMALL} color="text-secondary">
            {messages.machines?.["emptyZipFileErrorMessage"]}
          </BodyText>
        ) : (
          <>
            <BodyText
              color="text-tertiary"
              size={BODY_TEXT_SIZES.X_SMALL}
              className="!mb-sm mt-0 "
            >
              {messages.machines?.["selectParentFileCaption"]}
            </BodyText>
            <SearchInput
              placeholder={messages.machines?.["searchParentFile"]}
              value={searchQuery}
              onChange={setSearchQuery}
              className="w-full mt-0 mb-md"
              activeByDefault={true}
            />
            {filteredFilePaths.length === 0 ? (
              <BodyText
                size={BODY_TEXT_SIZES.X_SMALL}
                color="text-secondary text-center"
              >
                {messages.machines?.["emptySearchResultMessage"]}
              </BodyText>
            ) : (
              <div
                style={{
                  height: Math.min(400, filteredFilePaths.length * 80),
                }}
              >
                <AutoSizer>
                  {({ height, width }) => (
                    <List
                      height={height}
                      width={width}
                      deferredMeasurementCache={cache}
                      rowCount={filteredFilePaths.length}
                      rowHeight={cache.rowHeight}
                      rowRenderer={rowRenderer}
                      overscanRowCount={5}
                    />
                  )}
                </AutoSizer>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ZipFilePreview;
